<!--
 * @Author: xiaozhen <EMAIL>
 * @Date: 2025-02-24 12:24:08
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-23 10:43:53
 * @FilePath: /telesale-web_v2/apps/telesale-mobile/src/views/customer/details/components/Renew.vue
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
-->
<script lang="ts" setup>
import { usePriceDifference } from "@telesale/shared";
import { priceDiffFind, repurchaseFind } from "@/api/customer/details";
import { getAuth } from "@/utils/common/auth";
import { repurchaseTypeObj } from "@telesale/shared/src/businessHooks/payPush/utils";
import { useRouter } from "vue-router";
import RenewList from "./RenewList.vue";

interface Props {
  userid?: string;
  isShowOperation: boolean;
  type?: string;
}

const props = defineProps<Props>();
let isRepurchase = getAuth("telesale_admin_repurchase");
const router = useRouter();
const mode = import.meta.env.MODE;

const {
  loading,
  dataList,
  repurchaseType,
  listHeader,
  activePadList,
  commonXugouType,
  typeList,
  changeCommonXugouType,
  getEndTime
} = usePriceDifference({
  query: props,
  mode,
  repurchaseFind,
  isRepurchase,
  priceDiffFind
});

const showObj = ref({
  common: true
});

watch(
  () => loading.value,
  n => {
    loading.value = n;
  },
  {
    deep: true,
    immediate: true
  }
);

const goCode = (child, type: string) => {
  router.push({
    path: "/customer/qrCode",
    query: {
      type: "repurchase",
      repurchaseType: type,
      paidOrderId: child.paidOrderId,
      name: child.name,
      goodName: child.goodName,
      uri: child.payPage
    }
  });
};
</script>

<template>
  <div>
    <template v-if="type !== 'repurchase' || repurchaseType">
      <div class="flex p-10px mb-10px bg-#fff">
        续购类型：
        <van-radio-group
          v-model="commonXugouType"
          direction="horizontal"
          @change="changeCommonXugouType"
        >
          <van-radio
            icon-size="12"
            v-for="item in typeList"
            :key="item.value"
            :name="item.value"
          >
            {{ item.name }}
          </van-radio>
        </van-radio-group>
      </div>
      <RenewList
        v-model:show="showObj.common"
        :title="repurchaseTypeObj[repurchaseType]"
        :listHeader="listHeader"
        :dataList="dataList"
        :activePadList="activePadList"
        :repurchaseType="repurchaseType"
        :getEndTime="getEndTime"
        @goCode="goCode($event, repurchaseType)"
      />
    </template>
  </div>
</template>

<style lang="scss" scoped>
.d-tip-box {
  background-color: #eaf5ff;
  font-weight: bold;
  padding: 10px 20px;
  margin-bottom: 20px;
  font-size: 30px;
}
</style>
