<!--
 * @Date         : 2025-01-24 15:34:00
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { useRouter } from "vue-router";
import { useNewDiffPrice, usePriceDifference } from "@telesale/shared";
import { newDiffStage } from "@telesale/shared/src/data/customer";
import { closeToast, showLoadingToast, showToast } from "vant";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/store/modules/user";
import { getNewDiffPriceListApi } from "@/api/customer/diffPrice";
import { diffPriceColunms } from "@telesale/shared/src/businessHooks/newDiffPrice/data";
import { getDiffSettingApi } from "@/api/customer/linkSetting";

interface Props {
  userId: string;
}

const props = defineProps<Props>();

const router = useRouter();
const {
  loading,
  dataList,
  cloneData,
  searchForm,
  getData,
  resetData,
  searchData
} = useNewDiffPrice({
  userId: props.userId,
  getDiffPriceDataApi: getNewDiffPriceListApi,
  getDiffSettingApi
});

getData();

const goCode = (path: string, child) => {
  router.push({
    path,
    query: {
      type: "diffFind",
      orderId: child.orderId,
      name: child.name,
      goodName: child.cloneName,
      uri: child.payPage,
      cloneName: child.cloneName,
      strategyId: child.strategyId
    }
  });
};

const searchAmount = () => {
  if (searchForm.value.amount) {
    const amountValue = Number(searchForm.value.amount);
    if (!isFinite(amountValue)) {
      showToast({
        message: "金额需要为数字",
        duration: 3000
      });
      return;
    }
    if (amountValue <= 0 || amountValue > 99999999) {
      showToast({
        message: "金额需要大于0，小于99999999",
        duration: 3000
      });
      return;
    }
  }
  searchData();
};

watch(
  () => loading.value,
  n => {
    n ? showLoadingToast({}) : closeToast();
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div class="diff">
    <van-sticky :offset-top="44">
      <div class="bg-white py-2">
        <div class="px-2 gap-2 flex items-center">
          <div class="search-item w-50% flex justify-between">
            <!-- {{ searchForm.goodName || "请输入商品名称" }} -->
            <van-search
              v-model="searchForm.goodName"
              placeholder="请输入商品名称"
              class="w-100% p-0 bg-#eef2fb!"
              style="height: 22px"
              @search="searchData"
              :clearable="false"
            />

            <van-icon name="search" />
          </div>
          <div class="search-item w-50% flex justify-between">
            <van-search
              v-model.number="searchForm.amount"
              placeholder="请输入商品金额"
              class="w-100% p-0 bg-#eef2fb!"
              style="height: 22px"
              @search="searchAmount"
              :clearable="false"
            />

            <van-icon name="search" />
          </div>
        </div>
        <div class="px-20px mt-20px">
          <van-radio-group
            v-model="searchForm.bigVipType"
            direction="horizontal"
            @change="searchData"
          >
            <van-radio
              icon-size="12"
              v-for="item in newDiffStage"
              :key="item.value"
              :name="item.value"
            >
              {{ item.label }}
            </van-radio>
          </van-radio-group>
        </div>
      </div>
    </van-sticky>
    <template v-if="cloneData.length > 0">
      <div class="group-item" v-for="child in dataList" :key="child.id">
        <div class="good-name" v-html="child.skuGoodName" />
        <div class="flex flex-wrap">
          <div class="good-info">
            商品原价（划线价） ¥{{ child.originalAmount.toFixed(2) }}
          </div>
          <div class="good-info">
            商品直降 ¥{{ child.reducePrice.toFixed(2) }}
          </div>
          <div class="good-info">
            补差限时直降 ¥{{
              child.showSum ? child.directAmount.toFixed(2) : "-"
            }}
          </div>
          <div class="good-info">
            补差价抵扣 ¥{{ child.otherAmount.toFixed(2) }}
          </div>
          <div class="good-info">
            实付 {{ "¥ " + (child.amount - child.deductAmount).toFixed(2) }}
          </div>
        </div>
        <div>
          <div
            class="qrcode-btn"
            @click="goCode('/customer/qrCode', child)"
            v-if="child.showQR !== false"
          >
            动态二维码
          </div>
        </div>
      </div>
    </template>

    <van-empty v-else description="暂无数据" />
  </div>
</template>

<style lang="scss" scoped>
.diff {
  :deep(.van-search) {
    height: 44px;
    padding: 0;
  }
  :deep(.van-search__content) {
    padding-left: 0;
  }
  :deep(.van-field__control) {
    background-color: #eef2fb;
    color: #3370ff !important;
    &::placeholder {
      color: #3370ff !important;
      opacity: 1;
    }
  }
  :deep(.van-search__field .van-field__left-icon) {
    display: none;
  }
  .search-item {
    display: flex;
    align-items: center;
    padding: 8px 20px;
    background-color: #eef2fb;
    color: #3370ff;
    border-radius: 10px;
    height: 32px;
  }
  .show-title {
    padding: 10px 20px;
    background-color: #fae9fa;
    color: #6c0abc;
    border-radius: 10px;
    margin: 20px 0 20px 10px;
  }

  .group-item {
    padding: 20px 20px 20px 30px;
    border-bottom: 1px solid #b4b4b4;
    background-color: #fff;
    box-sizing: border-box;
  }
  .group-item:last-child {
    border-bottom: 0;
  }
  .good-name {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
  }
  .good-info {
    padding: 10px 20px;
    background-color: #fff9e9;
    color: #813300;
    font-size: 24px;
    border-radius: 10px;
    margin-right: 20px;
    margin-bottom: 10px;
  }
  .qrcode-btn {
    color: #3370ff;
    font-size: 24px;
    margin-top: 20px;
  }
}
.show-title {
  padding: 10px 20px;
  background-color: #fae9fa;
  color: #6c0abc;
  border-radius: 10px;
  margin: 20px 0 20px 10px;
}
.group {
  padding: 0 20px;
  border-radius: 20px;
  width: 100%;
  background-color: #fff;
  box-sizing: border-box;
  &-item {
    padding: 20px;
    border-bottom: 1px solid #b4b4b4;
  }
  &-item:last-child {
    border-bottom: 0;
  }
  .good-name {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
  }
  .good-info {
    padding: 10px 20px;
    background-color: #fff9e9;
    color: #813300;
    font-size: 24px;
    border-radius: 10px;
    margin-right: 20px;
    margin-bottom: 10px;
  }
  .qrcode-btn {
    color: #3370ff;
    font-size: 24px;
    margin-top: 20px;
  }
}
</style>
