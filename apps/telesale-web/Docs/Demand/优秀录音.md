# 优秀录音

## 文档内容总结

本文档描述了优秀录音功能的需求设计，旨在将复核完成的优质案例库录音自动同步到优秀录音页面，提升质检人员的工作效率。

**核心目标：** 复核完成的优质案例库录音自动进入优秀录音页面，减少人工筛选工作量

**主要变更内容：**
- **新增页面：** 在日常运营-呼叫记录下方新增优秀录音页面
- **自动同步：** 机器人通话评级为A级且人工审核通过的录音自动进入优秀录音库
- **查看功能：** 支持录音播放、对话转译明细查看等功能
- **筛选功能：** 支持按坐席名称、通话ID、时间范围等条件筛选

**技术要点：** 需要与AI案例库系统对接，实现自动数据同步和录音播放组件复用

---

## 一、修订记录

| 修改时间 | 修改原因 | 修改后 | 通知人 |
|---------|---------|--------|--------|
|         |         |        |        |

## 二、需求概述

### 现状
当前AI案例库判断准确率在60%左右，现在当前流程为人工复审完毕后，会对优质录音进行对外同步，需要质检人员手动筛选录音后进行同步，后续希望可以通过系统页面进行承载进行自动同步

### 需求目标
复核完成的优质案例库录音自动进入优秀录音页面

### 需求设计关键点
判断标准和流程

## 三、术语表

| 名称 | 术语场景 | 术语例 |
|------|---------|--------|
|      |         |        |

## 四、调整范围

| 功能名称 | 类型 | 补充说明 |
|---------|------|----------|
| 优秀录音 | 新增页面 |          |

## 五、功能需求描述

### 页面说明
**页面板块：** 日常运营-呼叫记录下方

### 进入来源条件说明
需要案例库中机器人通话评级为：A级，且需要人工审核结果状态为【通过】的录音，自动进入优秀录音库进行展示

### 操作功能说明

| 功能名称 | 功能类型 | 功能说明 | 交互说明 | 备注 |
|---------|---------|---------|---------|------|
| 坐席名称搜索 | 筛选器 | 支持按照坐席名称搜索通话 | 支持混合查询 |  |
| 通话ID搜索 | 筛选器 | 支持按照通话ID搜索通话 |  |  |
| 通话时间范围搜索 | 筛选器 | 支持选择通话时间范围搜索 |  |  |
| 时间范围搜索 | 筛选器 | 支持选择按照时间范围搜索 |  | 默认当月 |
| 通话ID复制按钮 | 按钮 | 支持一键复制通话ID | 点击按钮，toast提示：通话ID已复制到剪切板 |  |
| 查看通话 | 按钮 | 支持查看录音文件操作 | 点击后，打开查看通话弹窗 |  |
| 分页器 | 分页器 | 支持对列表进行分页 | 复用全局通用分页配置 |  |

### 列表字段说明

| 字段名称 | 字段说明 | 字段来源 | 备注 |
|---------|---------|---------|------|
| 通话ID | 优秀录音的通话ID | 根据指定条件清洗同步过来的优秀录音池 |  |
| 创建时间 | 通话创建的时间 | 对应优秀通话的创建时间 |  |
| 坐席名称 | 沟通坐席的名称 | 对应优秀通话的沟通坐席 |  |
| 组织架构 | 沟通坐席的组织架构 | 对应坐席的组织架构 |  |
| 通话时长 | 通话的总时长 | 对应通话的通话时长 |  |
| 评分总结 | 仅展示案例库中通话总结中-提起最后一部分【评分总结】内容 | AI案例库评分总结 | 超出部分展示...，鼠标移入展示悬浮气泡，匹配失败展示为空 |

### 查看明细弹窗

#### 优秀案例库-查看明细弹窗

##### 录音播放组件

| 功能名称 | 功能类型 | 功能说明 | 交互说明 |
|---------|---------|---------|---------|
| 关闭按钮 | 按钮 | 关闭弹窗 | 点击关闭按钮关闭弹窗 |
| 录音组件 | 组件 | 播放录音 | 复用录音组件，详细交互及限制可查看录音组件优化 |

##### 对话转译明细

| 功能名称 | 功能类型 | 功能说明 | 交互说明 |
|---------|---------|---------|---------|
| 对话转移明细 | 对话框 | 展示对应通话的文本转译明细 | 支持上下滑动展示转译全文 |

##### 评级说明明细
**评估总结-内容来源：** AI评分机器人返回

## 六、权限说明
增加页面权限

## 七、移动端兼容说明

## 八、相关文档

## 九、测试用例

## 十、需求复盘

### 数据详情

### 好的地方

### 不足的地方
