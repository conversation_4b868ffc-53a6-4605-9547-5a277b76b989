<!--
 * @Date         : 2024-12-13 17:51:50
 * @Description  : 坐席选择器
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";

const props = withDefaults(
  defineProps<{
    modelValue: number | undefined;
    clearable?: boolean;
    isAll?: boolean;
    placeholder?: string;
  }>(),
  {
    isAll: false,
    clearable: true,
    placeholder: "请选择坐席"
  }
);

const emit = defineEmits<{
  (e: "update:modelValue", value: number | undefined): void;
}>();

const workerId = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});

const { agentList, allAgentList } = storeToRefs(useUserStore());

const data = computed(() => {
  console.log(props.isAll);

  const list = props.isAll ? allAgentList.value : agentList.value;
  return list.map(item => ({
    label: item.name,
    value: item.id
  }));
});
</script>

<template>
  <el-select-v2
    v-model="workerId"
    filterable
    :clearable="props.clearable"
    :options="data"
    :placeholder="props.placeholder"
  />
</template>
