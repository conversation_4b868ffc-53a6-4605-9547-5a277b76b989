const Layout = () => import("/@/layout/index.vue");

const dailyRouter = {
  path: "/daily",
  component: Layout,
  redirect: "noRedirect",
  meta: {
    title: "日常运营"
  },
  children: [
    {
      path: "/daily/callRecord/index",
      name: "callRecord",
      component: () => import("/@/views/daily/callRecord/index.vue"),
      meta: {
        title: "呼叫记录",
        keepAlive: true
      }
    },
    {
      path: "/daily/excellentRecording/index",
      name: "excellentRecording",
      component: () => import("/@/views/daily/excellentRecording/index.vue"),
      meta: {
        title: "优秀录音",
        keepAlive: true
      }
    },
    {
      path: "/daily/updateNotice/index",
      name: "updateNotice",
      component: () => import("/@/views/daily/updateNotice/index.vue"),
      meta: {
        title: "版本公告",
        keepAlive: true
      },
      children: [
        {
          path: "/daily/updateNoticeDetails/index",
          name: "updateNoticeDetails",
          component: () =>
            import("/@/views/daily/updateNoticeDetails/index.vue"),
          meta: {
            title: "版本公告详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/daily/scheduling/index",
      name: "scheduling",
      component: () => import("/@/views/daily/scheduling/index.vue"),
      meta: {
        title: "排班",
        keepAlive: true
      }
    },
    {
      path: "/daily/qrcodeAttendance/index",
      name: "qrcodeAttendance",
      component: () => import("/@/views/daily/qrcodeAttendance/index.vue"),
      meta: {
        title: "活码坐席设置",
        keepAlive: true
      }
    },
    {
      path: "/daily/numberPool/index",
      name: "numberPool",
      component: () => import("/@/views/daily/numberPool/index.vue"),
      meta: {
        title: "号码池",
        keepAlive: true
      }
    },
    {
      path: "/daily/cancel/index",
      name: "cancel",
      component: () => import("/@/views/daily/cancel/index.vue"),
      meta: {
        title: "注销列表",
        keepAlive: true
      }
    },
    {
      path: "/daily/release/index",
      name: "release",
      component: () => import("/@/views/daily/release/index.vue"),
      meta: {
        title: "释放记录",
        keepAlive: true
      }
    },
    {
      path: "/daily/jobNOPool/index",
      name: "jobNOPool",
      component: () => import("/@/views/daily/jobNOPool/index.vue"),
      meta: {
        title: "工号池",
        keepAlive: true
      }
    },
    {
      path: "/daily/weChatWork/index",
      name: "WeChatWork",
      component: () => import("/@/views/daily/weChatWork/index.vue"),
      meta: {
        title: "企微账号管理",
        keepAlive: true
      }
    },
    {
      path: "/daily/miniprogramSet/index",
      name: "miniprogramSet",
      component: () => import("/@/views/daily/miniprogramSet/index.vue"),
      meta: {
        title: "小程序设置",
        keepAlive: true
      }
    },
    {
      path: "/daily/callWays/index",
      name: "callWays",
      component: () => import("/@/views/daily/callWays/index.vue"),
      meta: {
        title: "外呼渠道",
        keepAlive: true
      },
      children: [
        {
          path: "/daily/callWaysDetails/index",
          name: "callWaysDetails",
          component: () => import("/@/views/daily/callWaysDetails/index.vue"),
          meta: {
            title: "外呼渠道详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/daily/callTeam/index",
      name: "CallTeam",
      component: () => import("/@/views/daily/callTeam/index.vue"),
      meta: {
        title: "外呼团队管理",
        keepAlive: true
      },
      children: [
        {
          path: "/daily/callTeamDetails/index",
          name: "CallTeamDetails",
          component: () => import("/@/views/daily/callTeamDetails/index.vue"),
          meta: {
            title: "外呼团队管理详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/daily/profile/index",
      name: "Profile",
      component: () => import("/@/views/daily/profile/index.vue"),
      meta: {
        title: "专属资料管理",
        keepAlive: true
      }
    },
    {
      path: "/daily/linkPoster/index",
      name: "LinkPoster",
      component: () => import("/@/views/daily/linkPoster/index.vue"),
      meta: {
        title: "课程链接海报",
        keepAlive: true
      }
    },
    {
      path: "/daily/linkPosterDetails/index",
      name: "LinkPosterDetails",
      component: () => import("/@/views/daily/linkPosterDetails/index.vue"),
      meta: {
        title: "课程链接海报详情",
        dynamicLevel: 1,
        keepAlive: true
      }
    },
    {
      path: "/daily/interface/index",
      name: "interface",
      component: () => import("/@/views/daily/interface/index.vue"),
      meta: {
        title: "接口管理",
        keepAlive: true
      }
    },
    {
      path: "/daily/operateLog/index",
      name: "operateLog",
      component: () => import("/@/views/daily/operateLog/index.vue"),
      meta: {
        title: "操作日志",
        keepAlive: true
      }
    }
  ]
};

export default dailyRouter;
