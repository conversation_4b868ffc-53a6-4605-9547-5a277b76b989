<!--
 * @Date         : 2025-01-21 16:00:08
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { newDiffStage } from "@telesale/shared/src/data/customer";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useNewDiffPrice } from "@telesale/shared";
import { diffPriceColunms } from "@telesale/shared/src/businessHooks/newDiffPrice/data";
import { deviceDetection } from "/@/utils/deviceDetection";
import { OperationObj } from "/@/components/ReTable/types";
import {
  DiffPriceInfo,
  getNewDiffPriceListApi
} from "/@/api/customer/diffPrice";
import DeductibleOrders from "./dialog/DeductibleOrders.vue";
import CreateQrCode from "./dialog/CreateQrCode.vue";
import { getDiffSettingApi } from "/@/api/customer/linkSetting";

const props = defineProps<{
  userId: string;
}>();

const isModal = ref(false);
const qrcodeModal = ref(false);
const rowData = ref<DiffPriceInfo>();

const {
  loading,
  dataList,
  cloneData,
  searchForm,
  getData,
  resetData,
  searchData,
  getDemarcationTime
} = useNewDiffPrice({
  userId: props.userId,
  getDiffPriceDataApi: getNewDiffPriceListApi,
  getDiffSettingApi
});

getData();

const operation: OperationObj[] = [
  {
    text: "动态二维码",
    eventFn: row => createQrCode(row),
    isShow: row => row.canJoin && row.showQR !== false
  }
];

const createQrCode = (row: any) => {
  rowData.value = row;
  qrcodeModal.value = true;
};

const openDeduct = (row: DiffPriceInfo) => {
  rowData.value = row;
  isModal.value = true;
};

const reset = () => {
  resetData();
};
</script>

<template>
  <div v-loading="loading">
    <template v-if="cloneData.length > 0">
      <el-form :model="searchForm" inline @submit.prevent>
        <el-form-item label="">
          <el-input
            v-model="searchForm.goodName"
            clearable
            placeholder="请输入商品名称"
            @keyup.enter="searchData"
          />
        </el-form-item>
        <el-form-item label="">
          <el-input-number
            v-model="searchForm.amount"
            clearable
            placeholder="请输入商品价格"
            style="width: 200px"
            @keyup.enter="searchData"
          />
        </el-form-item>
        <el-form-item label="">
          <el-button
            type="primary"
            :icon="useRenderIcon('search')"
            @click="searchData"
          >
            搜索
          </el-button>
          <el-button :icon="useRenderIcon('refresh')" @click="reset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <el-radio-group v-model="searchForm.bigVipType" @change="searchData">
        <el-radio
          v-for="(item, index) in newDiffStage"
          :key="index"
          :label="item.value"
        >
          {{ item.label }}
        </el-radio>
      </el-radio-group>
    </template>
    <div class="c-red font-bold mb-10px" v-if="getDemarcationTime()">
      升单优惠直降截止时间：{{ getDemarcationTime() }}
    </div>
    <div class="g-table-box">
      <ReTable
        v-if="!deviceDetection()"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="diffPriceColunms"
        :operation="operation"
        :width-operation="130"
      >
        <template #otherAmount="{ row }">
          <el-button type="primary" link @click="openDeduct(row)">
            ¥ {{ row.otherAmount?.toFixed(2) }}
          </el-button>
        </template>
      </ReTable>
      <ReCardList
        v-else
        ref="cardRefs"
        :dataList="dataList"
        :listHeader="diffPriceColunms"
        :operation="operation"
      >
        <template #otherAmount="{ row }">
          <el-button type="primary" link @click="openDeduct(row)">
            ¥ {{ row.otherAmount?.toFixed(2) }}
          </el-button>
        </template>
      </ReCardList>
      <DeductibleOrders
        v-if="isModal"
        v-model:value="isModal"
        :data="rowData.deductibleOrders"
        :maxDeductAmount="rowData.maxDeductAmount"
        :showSum="rowData.showSum"
      />
      <CreateQrCode
        v-if="qrcodeModal"
        v-model:value="qrcodeModal"
        :data="rowData"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item .el-input-number .el-input) {
  width: 200px;
}
</style>
