<!--
 * @Date         : 2024-05-15 16:13:18
 * @Description  : 共用表格组件
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { ref, onActivated, inject, Ref, watch, nextTick, computed } from "vue";
import { storeToRefs } from "pinia";
import { useTable } from "/@/hooks/useTable";
import { useAppStore } from "/@/store/modules/app";
import {
  TabEnum,
  TabType,
  getColumnsList,
  operation,
  tabList
} from "../data/index";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { useDetail } from "/@/utils/handle/customDetails";
import Search from "./Search.vue";
import { cloneDeep, isBoolean } from "lodash-es";
import { TableColumns } from "/@/components/ReTable/types";
import { getOngingList } from "/@/api/customer";
import { getAuth } from "/@/utils/auth";
import SettingFiled from "/@/components/SettingFiled/index.vue";
import DialogTransfer from "/@/components/DialogTransfer/index.vue";
import { ElMessage } from "element-plus";
import { useCollectStore } from "/@/store/modules/collect";
import { formatTime } from "/@/utils/common";
import { getFamilyCategoryData } from "/@/utils/common";
import { hideFiled } from "../data/index";
import CustomerDrawer from "/@/views/customer/components/CustomerDrawer/index.vue";
import { useCustomerDrawer } from "/@/views/customer/hooks/useCustomerDrawer";

const props = defineProps<{
  type: TabType;
}>();

const { device } = storeToRefs(useAppStore());
const { setCollectClue, updateClueCollect } = useCollectStore();
const { toDetail } = useDetail();
const searchRef = ref<InstanceType<typeof Search>>();
const columnsList = ref(getColumnsList(props.type));
const columns = ref<TableColumns[]>([]);
const active = inject<Ref<TabType>>("active");
const isModelItem = ref<boolean>(false);
const isTransferModel = ref<boolean>(false);
const ids = ref<any[]>([]);
const dataMemory = ref();
const tableRefs = ref();
const rowIndex = ref<number>();
const { type } = props;
const { drawerIdList, isCustomerVisible, setIdList } = useCustomerDrawer();

const firstTab = tabList.find(item => getAuth(item.auth))?.value;

const currentTab = computed(() => {
  return tabList.find(item => item.value === type);
});

const { loading, dataList, onSearch, searchForm, handlerQuery, Pagination } =
  useTable({
    api: getOngingList,
    immediate: props.type !== firstTab,
    initParams: {
      payCategory: currentTab.value?.payCategory || undefined,
      featureTag: currentTab.value?.featureTag || undefined,
      excludeTags: currentTab.value?.excludeTags || undefined,
      callCount: -1,
      openCal: 14,
      hasPaid: true
    },
    beforeRequest: data => {
      data.notDial = data.firstDialDuration;
      if (data.orgId && data.workerid) {
        data.orgId = undefined;
      }
      data.familyCategory = getFamilyCategoryData(data.familyCategory);
      return data;
    },
    dataCallback: async res => {
      setIdList(res.data.list);
      const infoids = res.data.list.map(item => item.infoUuid);
      res.data.list = await setCollectClue(infoids, res.data.list);
    },
    endCallback: () => {
      setCurrent(rowIndex.value);
    }
  });

watch(
  () => active.value,
  n => {
    nextTick(() => {
      if (device.value !== "mobile") {
        rowIndex.value = tableRefs.value?.getClickRow();
      }

      if (n === type) {
        handlerQuery();
      }
    });
  }
);

//表头重置
function resetFilter() {
  tableRefs.value?.resetFilter();
}
//排序重置
function clearSort() {
  tableRefs.value?.clearSort();
}

const reset = () => {
  searchRef.value.resetForm();
};

//表头筛选
const filterChange = row => {
  for (const key in row) {
    searchRef.value.searchForm[key] = row[key]?.[0];
  }
  getList();
};

const filterHeadData = (filterData: Record<string, any>) => {
  for (const key in filterData) {
    searchRef.value.searchForm[key] = filterData[key];
  }
  getList();
};

const sortChange = column => {
  if (column.prop) {
    searchRef.value.searchForm.orderBy = column.prop.replace(
      /([A-Z])/g,
      function (match) {
        return "_" + match.toLowerCase();
      }
    );
    searchRef.value.searchForm.sort = column.order
      ? column.order.slice(0, -6)
      : "";
    searchRef.value.searchForm.combSort = [];
    getList();
  }
};

const formatData = form => {
  const data = cloneDeep(form);
  data.workerid = data.workerid ? data.workerid + "" : undefined;
  data.hasOrder = isBoolean(data.hasOrder) ? data.hasOrder : undefined;
  formatTime(data, "userExpireStart", "userExpireEnd", "clueTime");
  formatTime(data, "deadlineStart", "deadlineEnd", "deadline");
  formatTime(data, "lastActiveStart", "lastActiveEnd", "watchTime");
  formatTime(data, "start", "end", "createTime");
  formatTime(data, "lastDialStart", "lastDialEnd", "lastDialTime");
  formatTime(data, "regTimeStart", "regTimeEnd", "regTime");
  formatTime(data, "lastDealingStart", "lastDealingEnd", "lastDealing");
  formatTime(data, "lastPaidTimeStart", "lastPaidTimeEnd", "lastPaidTime");
  formatTime(data, "authEndAtStart", "authEndAtEnd", "authTime");
  if (!data.callCount && data.callCount !== 0) {
    data.callCount = -1;
  }
  return data;
};

const getList = () => {
  rowIndex.value = undefined;
  const data = formatData(searchRef.value.searchForm);
  searchForm.value = data;
  onSearch();
};

const transferMore = () => {
  let cus = tableRefs.value?.handleSelectionChange();

  if (!cus.length) {
    ElMessage.warning("您未选择任何线索");
    return;
  }
  ids.value = cus;
  dataMemory.value = {};
  isTransferModel.value = true;
};

function openCustomerDrawer(row) {
  dataMemory.value = row;
  isCustomerVisible.value = true;
}

function parantMath({ key, params }) {
  switch (key) {
    case "openCustomerDrawer":
      openCustomerDrawer(params);
      break;
  }
}

//回显上一次点击详情或者申请的用户
const setCurrent = (row: any) => {
  if (!row) {
    return;
  }
  let index = dataList.value.findIndex(item => item.id === row.id);
  if (index < 0) {
    return;
  }
  tableRefs.value?.setCurrent(index);
};

const updateCollect = (
  id: number,
  infoUuid: string,
  action: "add" | "delete"
) => {
  dataList.value = updateClueCollect(id, infoUuid, action, dataList.value);
};

onActivated(() => {
  if (device.value !== "mobile") {
    rowIndex.value = tableRefs.value?.getClickRow();
  }

  if (active.value === type) {
    handlerQuery();
  }
});
</script>

<template>
  <div v-loading="loading">
    <Search
      ref="searchRef"
      :type="props.type"
      @onSearch="getList"
      @clearSort="clearSort"
      @resetFitler="resetFilter"
      @transferMore="transferMore"
    />
    <ReTable
      v-if="device !== 'mobile'"
      ref="tableRefs"
      :dataList="dataList"
      :listHeader="columns"
      :sort-change="sortChange"
      :filterChange="filterChange"
      :selection="getAuth('telesale_admin_custom_transfer')"
      @filterHeadData="filterHeadData"
      @parantMath="parantMath"
    >
      <template #appendColumn>
        <el-table-column fixed="right" label="操作" width="180">
          <template #header>
            <span>操作</span>
            <IconifyIconOffline
              icon="setting"
              @click="isModelItem = true"
              style="
                cursor: pointer;
                color: #409eff;
                font-size: 25px;
                position: absolute;
                right: 15px;
              "
            />
          </template>
          <template #default="{ row }">
            <template v-for="(item, index) in operation(toDetail)" :key="index">
              <template v-if="item.event">
                <CollectModal
                  :collectInfo="row.collectInfo"
                  v-if="item?.isShow?.(row)"
                  @success="updateCollect($event, row.infoUuid, 'add')"
                  @remove="updateCollect($event, row.infoUuid, 'delete')"
                >
                  <el-button link type="primary">
                    {{ row.collectInfo?.id ? "已收藏" : "收藏" }}
                  </el-button>
                </CollectModal>
              </template>
              <el-button v-else type="primary" link @click="item.eventFn(row)">
                {{ item.text }}
              </el-button>
            </template>
          </template>
        </el-table-column>
      </template>
    </ReTable>
    <ReCardList
      v-else
      ref="cardRefs"
      :dataList="dataList"
      :listHeader="columnsList"
      :operation="operation(toDetail)"
      @parantMath="parantMath"
    />
    <div class="mt-10px">
      <Pagination />
    </div>
    <SettingFiled
      :localKey="'payClue' + props.type + '-column'"
      v-model:visible="isModelItem"
      v-model:columns="columns"
      :listHeader="columnsList"
      :hideFiled="hideFiled"
      @success="reset"
    />
    <CustomerDrawer
      v-if="isCustomerVisible"
      v-model:value="isCustomerVisible"
      :infoUuid="dataMemory!.infoUuid"
      :idList="drawerIdList"
    />
    <DialogTransfer
      v-model:value="isTransferModel"
      :msg="dataMemory"
      :id="ids"
      :transferType="true"
      type="转线索"
      @getList="getList"
      v-if="isTransferModel"
    />
  </div>
</template>

<style lang="scss" scoped></style>
