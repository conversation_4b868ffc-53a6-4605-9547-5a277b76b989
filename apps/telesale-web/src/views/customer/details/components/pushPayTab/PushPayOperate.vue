<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import AuthDetail from "/@/components/DiffFind/dialog/AuthDetail.vue";
import { getTagListApi } from "/@/api/customer";
import { getCourseListApi } from "/@/api/customer/exclusiveLink";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStore } from "/@/store/modules/user";
import {
  priceDiffFind,
  priceDiffFindNew,
  pushPay,
  repurchaseFind
} from "/@/api/customerDetails";

import { useLinkLists } from "/@/views/customer/link/utils/linkLists";
import { bigVipList, newDiffStage } from "@telesale/shared/src/data/customer";
import { usePayPush, getNewDuration, useNewSyncLink } from "@telesale/shared";
import { getHasIpadApi, getGoodConfigApi } from "/@/api/customer/details";
import { getAuth } from "/@/utils/auth";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { courseTypeList } from "@telesale/shared/src/data/pushPay";
import { storeToRefs } from "pinia";
import { findPosition } from "/@/api/agent";
import { getLabel } from "/@/utils/common";
import { isExperimentGroupWorkerApi } from "/@/api/system/distribute";
import {
  DiffPriceInfo,
  getNewDiffPriceListApi
} from "/@/api/customer/diffPrice";
import DeductibleOrders from "/@/businessComponents/DiffPirce/dialog/DeductibleOrders.vue";
import {
  getDiffSettingApi,
  getVenueLinkGoodsListApi
} from "/@/api/customer/linkSetting";
import { useVisibleLink } from "/@/hooks/business/useVisibleLink";
import { getGoodInfoApi, getSyncDataApi } from "/@/api/customer/exclusiveLink";

let { schoolYearList, getStageGood } = useLinkLists();

interface Props {
  onionid: string;
  userid: string;
  pushDiscover: boolean;
  stage: string;
  grade: string;
}

interface Emits {
  (e: "handleClose"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

function handleClose() {
  emit("handleClose");
}

let isDiffUpdate = getAuth("telesale_admin_diff_update");

let isRepurchase = getAuth("telesale_admin_repurchase");

let device = useAppStoreHook().device;
const { isStages, userMsg } = storeToRefs(useUserStore());
const ruleFormRef = ref<FormInstance>();

const { loading: showLinkLoading, hasPermissions } = useVisibleLink();

const {
  rules,
  showList,
  showNewList,
  linkFilterData,
  typeList,
  loading,
  bigVipType,
  form,
  goodList,
  ipadTypeList,
  syncData,
  blocksGood,
  goodInfo,
  changeTypeHandle,
  changeTarget,
  changeType,
  filterDiff,
  filterNewDiff,
  changeBigVip,
  changeNewStage,
  handleData,
  changeStage,
  changeInstallment,
  changeGood,
  changeBlockSchoolYear,
  changeblocksGood,
  changeUpgrade
} = usePayPush({
  mode: import.meta.env.MODE,
  userId: props.userid,
  onionid: props.onionid,
  stageInfo: {
    stage: props.stage,
    grade: props.grade
  },
  priceDiffFindNew,
  priceDiffFind,
  repurchaseFind,
  getCourseListApi,
  getNewDiffPriceListApi,
  getGoodConfigApi,
  getTagListApi,
  getDiffSettingApi,
  getGoodInfoApi,
  getSyncDataApi,
  getOrgData: () => findPosition({ key: "id", value: userMsg.value.id + "" }),
  isExperimentGroupWorkerApi: () =>
    isExperimentGroupWorkerApi({ workerId: userMsg.value.id }),
  getVenueLinkGoodsListApi
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl || loading.value) return;
  await formEl.validate(async valid => {
    if (valid) {
      ElMessageBox.confirm("确定进行支付推送吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          loading.value = true;
          try {
            const data = handleData();
            data.pushDiscover = props.pushDiscover;
            pushPay(data)
              .then(() => {
                loading.value = false;
                ElMessage.success("操作成功");
                handleClose();
              })
              .catch(() => {
                loading.value = false;
              });
          } catch (error) {
            loading.value = false;
            console.log(error);
          }
        })
        .catch(() => {
          ElMessage.info("已取消操作");
        });
    } else {
      return false;
    }
  });
};

let isModelDetail = ref<boolean>(false);
let dataMemery = ref();
const isModal = ref(false);
const rowData = ref<DiffPriceInfo>();
function openDetail() {
  if (form.pushType === "difference_v6") {
    isModal.value = true;
    rowData.value = form.target as any;
  } else {
    dataMemery.value = form.target;
    isModelDetail.value = true;
  }
}
</script>

<template>
  <el-form
    :model="form"
    label-suffix="："
    :label-width="device !== 'mobile' ? '180px' : ''"
    ref="ruleFormRef"
    :class="{ mobile: device === 'mobile' }"
    :rules="rules"
    v-loading="loading"
  >
    <div class="bg-#ecf5fd p-10px font-bold mb-10px" v-if="props.pushDiscover">
      说明：一天只能推送3次，当日推送的所有商品都会展示在APP【我的购买】中，最新的一条商品会同时展示在【发现页】，推送的商品链接当天24点后过期
    </div>
    <el-row>
      <el-col :lg="1" />
      <el-col :lg="19">
        <el-form-item label="洋葱ID" prop="onionid">
          <el-input v-model="form.onionid" disabled />
        </el-form-item>
        <el-form-item prop="pushType" label="购买类型">
          <el-radio-group v-model="form.pushType" @change="changeType">
            <el-radio label="link">专属链接</el-radio>
            <el-radio
              label="blocksGood"
              v-if="
                getAuth('telesale_admin_new_exclusiveLink_newCreateLink') &&
                hasPermissions &&
                !showLinkLoading
              "
            >
              会场链接（新）
            </el-radio>
            <el-radio
              label="exclusiveLink"
              v-if="
                getAuth('telesale_admin_new_exclusiveLink_createLink') &&
                hasPermissions &&
                !showLinkLoading
              "
            >
              会场链接
            </el-radio>
            <el-radio
              label="difference_v6"
              v-if="getAuth('telesale_admin_diff_price')"
            >
              补差价（新）
            </el-radio>
            <el-radio label="repurchase" v-if="isRepurchase">续购</el-radio>
            <!-- <el-radio
              label="zhufengPre2024"
              v-if="getAuth('telesale_admin_exclusiveLink_discountsCard')"
            >
              省钱卡
            </el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="续购类型"
          prop="type"
          v-if="form.pushType === 'repurchase' && typeList.length > 0"
        >
          <!-- {{ labelObj[form.type] || "" }} -->
          <el-radio-group v-model="form.type" @change="changeTypeHandle">
            <el-radio
              v-for="item in typeList"
              :key="item.value"
              :label="item.value"
            >
              {{ item.name }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item
          v-show="
            form.pushType === 'link' && hasPermissions && !showLinkLoading
          "
          prop="isNewExclusiveLink"
          label="是否为会场链接"
        >
          <el-switch v-model="form.isNewExclusiveLink" @change="clearValue" />
        </el-form-item> -->
        <template v-if="form.pushType === 'blocksGood'">
          <el-form-item
            prop="schoolYear"
            label="年级"
            :rules="{
              required: true,
              message: '请选择年级',
              trigger: 'blur'
            }"
          >
            <el-radio-group
              v-model="form.schoolYear"
              @change="changeBlockSchoolYear"
            >
              <el-radio
                v-for="(item, index) in syncData"
                :key="index"
                :label="item.schoolYear"
              >
                {{
                  item.schoolYear === "三年级" ? "一到三年级" : item.schoolYear
                }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            prop="target"
            label="商品"
            :rules="{
              required: true,
              message: '请选择课程',
              trigger: 'blur'
            }"
          >
            <el-select
              v-model="form.target"
              placeholder="请选择商品"
              value-key="id"
              filterable
              @change="changeblocksGood"
            >
              <el-option
                v-for="(item, index) in blocksGood"
                :key="index"
                :label="item.strategyName"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="isPackGood"
            label="打包购买首购+升单商品"
            v-if="form.target?.packGood?.length"
          >
            <el-radio-group v-model="form.isPackGood" @change="changeUpgrade">
              <el-radio :label="false">否</el-radio>
              <el-radio :label="true">是</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            prop="addPad"
            label="加购平板"
            v-if="form.target?.addContent?.length"
          >
            <el-radio-group v-model="form.addPad" @change="changeUpgrade">
              <el-radio
                :label="item.label"
                v-for="item in form.target?.addContent"
                :key="item.label"
              >
                {{ item.name }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <template v-if="goodInfo">
            <el-form-item label="商品原价">
              ¥ {{ goodInfo.originalAmount?.toFixed(2) }}
            </el-form-item>
            <el-form-item label="商品售价">
              ¥ {{ goodInfo.amount?.toFixed(2) }}
            </el-form-item>
            <el-form-item label="用户实付价">
              ¥ {{ (goodInfo.amount - goodInfo.deductAmount)?.toFixed(2) }}
            </el-form-item>
          </template>
        </template>

        <template v-else>
          <el-form-item
            label="课程名称"
            prop="target"
            v-if="
              !form.isNewExclusiveLink && form.pushType !== 'zhufengPre2024'
            "
            :rules="{
              required: true,
              message: '请选择课程',
              trigger: 'blur'
            }"
          >
            <div style="display: flex; width: 100%">
              <template v-if="form.pushType === 'difference_v4'">
                <el-select
                  v-model="bigVipType"
                  @change="changeBigVip"
                  class="prefix-select"
                  style="width: 40%"
                >
                  <el-option
                    v-for="(item, idx) in bigVipList"
                    :key="idx"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <template v-if="form.pushType === 'difference_v6'">
                <el-select
                  v-model="bigVipType"
                  @change="changeNewStage"
                  class="prefix-select"
                  style="width: 40%"
                >
                  <el-option
                    v-for="(item, idx) in newDiffStage"
                    :key="idx"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
              <!-- <template v-if="form.pushType === 'link'">
              <el-select
                v-model="courseType"
                @change="changeCourseType"
                class="prefix-select"
                style="width: 40%"
              >
                <el-option
                  v-for="(item, idx) in courseTypeList"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template> -->
              <el-select
                v-if="
                  form.pushType.indexOf('difference_v4') > -1 ||
                  form.type === 'activity'
                "
                ref="selectGroupRef"
                v-model="form.target"
                placeholder="请搜索课程名称或价格"
                filterable
                remote
                :remote-method="filterDiff"
                remote-show-suffix
                value-key="id"
                style="width: 100%"
                popper-class="g-select-group"
                @change="changeTarget"
              >
                <el-option-group
                  v-for="group in showList"
                  :key="group.value"
                  :label="group.label"
                  v-show="group.options.length"
                >
                  <el-option
                    v-for="item in group.options"
                    :key="item.id"
                    :label="item.name"
                    :value="item"
                  >
                    <span v-html="item.name" />
                  </el-option>
                </el-option-group>
              </el-select>
              <el-select
                v-else-if="form.pushType === 'difference_v6'"
                ref="selectRef"
                v-model="form.target"
                placeholder="请搜索课程名称 "
                filterable
                value-key="id"
                remote
                :remote-method="filterNewDiff"
                remote-show-suffix
                popper-class="g-select-group"
                style="width: 100%"
                @change="changeTarget"
              >
                <el-option
                  v-for="(item, idx) in showNewList"
                  :key="idx"
                  :label="item.nameInit"
                  :value="item"
                >
                  <span v-html="item.name" />
                </el-option>
              </el-select>
              <el-select
                v-else
                ref="selectRef"
                v-model="form.target"
                placeholder="请搜索课程名称"
                filterable
                value-key="id"
                style="width: 100%"
              >
                <el-option
                  v-for="item in linkFilterData"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                />
              </el-select>
            </div>
          </el-form-item>
          <template v-if="form.isNewExclusiveLink">
            <el-form-item prop="stage" label="学段">
              <el-radio-group v-model="form.stage" @change="changeStage">
                <el-radio
                  v-for="item in goodList"
                  :key="item.cname"
                  :label="item.cname"
                >
                  {{ item.name }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              prop="goods"
              label="商品"
              style="width: 100%"
              :rules="{
                required: true,
                message: '请选择商品',
                trigger: 'change'
              }"
            >
              <el-select
                v-model="form.goods"
                placeholder="请选择商品"
                value-key="name"
                clearable
                filterable
                @change="changeGood"
              >
                <el-option
                  v-for="(item, index) in getStageGood(goodList, form.stage)"
                  :key="index"
                  :label="item.name"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="exchange" label="平板" v-if="form.goods">
              <el-radio-group v-model="form.exchange">
                <el-radio
                  v-for="item in ipadTypeList"
                  :key="item.value"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="商品售价" v-if="form.exchange">
              {{ getLabel(form.exchange, ipadTypeList, "amount", "value") }}
            </el-form-item>
          </template>
          <template v-if="form.pushType !== 'link' && form.target">
            <template v-if="form.pushType === 'difference_v6'">
              <el-form-item label="商品原价（划线价）" class="is-required">
                <span>{{ form.target?.originalAmount?.toFixed(2) }}</span>
              </el-form-item>
              <el-form-item label="商品直降" class="is-required">
                <span>{{ form.target?.reducePrice?.toFixed(2) }}</span>
              </el-form-item>
              <el-form-item label="补差限时直降" class="is-required">
                <span>
                  {{
                    form.target?.showSum
                      ? form.target?.directAmount?.toFixed(2)
                      : ""
                  }}
                </span>
              </el-form-item>
              <el-form-item label="补差价抵扣" class="is-required">
                <span>{{ form.target?.otherAmount?.toFixed(2) }}</span>
                <IconifyIconOffline
                  @click="openDetail"
                  icon="question-line"
                  color="#409eff"
                  style="font-size: 22px"
                />
              </el-form-item>
              <el-form-item label="实付" class="is-required">
                <span>
                  {{
                    (form.target.amount - form.target.deductAmount).toFixed(2)
                  }}
                </span>
              </el-form-item>
            </template>
            <template v-else>
              <el-form-item
                prop="schoolYear"
                label="年级"
                style="width: 100%"
                v-if="
                  form.target?.strategyType === 'xinxiActivityBase' ||
                  form.target?.strategyType === 'xinxiActivityPro' ||
                  form.target.strategyType === '2023double11'
                "
                v-show="!form.type || form.type === 'activity'"
                :rules="{
                  required: true,
                  message: '请选择年级',
                  trigger: 'change'
                }"
              >
                <el-select
                  v-model="form.schoolYear"
                  placeholder="请选择年级"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="(item, index) in schoolYearList"
                    :key="index"
                    :label="item.text"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <template v-if="form.pushType === 'repurchase'">
                <el-form-item label="推荐">
                  <span>{{ form.target?.recommend || "-" }}</span>
                </el-form-item>
                <el-form-item label="商品原价">
                  <span>{{ form.target?.OriginAmount }}</span>
                </el-form-item>
              </template>
              <el-form-item label="商品售价" class="is-required">
                <span>{{ form.target?.amount }}</span>
              </el-form-item>
              <template v-if="!form.type || form.type === 'activity'">
                <el-form-item label="折算金额" class="is-required">
                  <span>{{ form.target?.discountPrice }}</span>
                  <IconifyIconOffline
                    v-if="
                      form.pushType === 'difference_v4' ||
                      form.type === 'activity'
                    "
                    @click="openDetail"
                    icon="question-line"
                    color="#409eff"
                    style="font-size: 22px"
                  />
                </el-form-item>
                <el-form-item label="实付金额" class="is-required">
                  <span>{{ form.target?.diffPrice }}</span>
                </el-form-item>
              </template>
            </template>

            <!-- <template
            v-if="
              form.type === 'activity' ||
              form.type === 'primary' ||
              form.type === 'highActivity' ||
              form.type === 'highPrimary'
            "
          >
            <el-form-item
              v-if="form.type === 'highActivity' || form.type === 'highPrimary'"
              label="原订单金额"
              class="is-required"
            >
              <span>{{ form.target?.paidAmount }}</span>
            </el-form-item>
            <el-form-item v-else label="原平板商品金额" class="is-required">
              <span>{{ form.target?.paidAmount }}</span>
            </el-form-item>
            <el-form-item label="用户支付金额" class="is-required">
              <span>{{ form.target?.shouldPay }}</span>
            </el-form-item>
          </template> -->
          </template>
        </template>

        <template v-if="isStages && form.pushType !== 'zhufengPre2024'">
          <el-form-item label="分期支付" prop="isInstallment">
            <el-radio-group
              v-model="form.isInstallment"
              @change="changeInstallment"
            >
              <el-radio
                v-for="(item, index) in isStagesList"
                :key="index"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="分期支付方式"
            prop="installmentPayType"
            v-if="form.isInstallment === 1"
          >
            <el-checkbox-group v-model="form.installmentPayType">
              <el-checkbox
                v-for="(item, index) in stagesType"
                :key="index"
                :label="item.value"
              >
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </template>
      </el-col>
      <el-col :lg="4" />
    </el-row>
    <div class="el-dialog__footer">
      <el-button type="primary" @click="submitForm(ruleFormRef)">
        确定
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </div>
  </el-form>
  <AuthDetail
    ref="authDetailRef"
    v-if="isModelDetail"
    v-model:value="isModelDetail"
    :dataMemery="dataMemery"
  />
  <DeductibleOrders
    v-if="isModal"
    v-model:value="isModal"
    :data="rowData.deductibleOrders"
    :maxDeductAmount="rowData.maxDeductAmount"
    :showSum="rowData.showSum"
  />
</template>
<style scoped lang="scss">
:deep(.el-form-item .el-input) {
  width: 100%;
}
</style>
