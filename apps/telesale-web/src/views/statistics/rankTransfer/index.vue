<!--
 * @Date         : 2024-11-27 16:34:00
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { ref } from "vue";
import Person from "./components/Person.vue";
import Group from "./components/Group.vue";

const type = ref("个人");
const referralType = ref(1);
</script>

<template>
  <div class="g-margin-20 g-tabs-box">
    <el-tabs v-model="type" type="border-card" class="d-tabs">
      <el-tab-pane label="个人" name="个人">
        <div class="g-margin-20 g-tabs-box">
          <el-tabs v-model="referralType" type="card" tab-position="top">
            <el-tab-pane label="销售" :name="1">
              <Person :referralType="referralType" />
            </el-tab-pane>
            <el-tab-pane label="伴学" :name="2" lazy>
              <Person :referralType="referralType" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      <el-tab-pane label="小组" name="小组" lazy>
        <Group />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
