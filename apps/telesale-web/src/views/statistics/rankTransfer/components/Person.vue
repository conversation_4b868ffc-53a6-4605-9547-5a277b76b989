<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "/@/store/modules/user";
import { rankPersonTransfer } from "/@/api/statistics_rank";
import paramsHandle from "/@/utils/handle/paramsHandle";
import originDate from "/@/utils/handle/originDate";
import RankTable from "/@/components/RankTable/index.vue";
import nameLength from "../../rankPerson/utils/nameLength";

const props = defineProps<{
  referralType?: number;
}>();

let allAgentObj = useUserStoreHook().allAgentObj;

const tableHeader = [
  { field: "amount", desc: "营收/元" },
  { field: "convRate", desc: "转化率" },
  { field: "userCount", desc: "裂变新客" }
];

const loading = ref(true);
const tableData = ref([]);

//form查询
const form = reactive({
  time: originDate(),
  referralType: props.referralType
});

function getList() {
  loading.value = true;
  rankPersonTransfer(paramsHandle(form, { newTime: true }))
    .then(({ data }: { data: any }) => {
      let list = [];
      for (let i = 0; i < 20; i++) {
        let amount = data.amount[i],
          convRate = data.convRate[i],
          userCount = data.userCount[i];
        let item = {
          amount: amount
            ? nameLength(allAgentObj[amount.id]?.name) + `（${amount.value}）`
            : "",
          convRate: convRate
            ? nameLength(allAgentObj[convRate.id]?.name) +
              `（${(convRate.value * 100).toFixed(2)}%）`
            : "",
          userCount: userCount
            ? nameLength(allAgentObj[userCount.id]?.name) +
              `（${userCount.value}）`
            : ""
        };
        list.push(item);
      }
      tableData.value = list;
      loading.value = false;
    })
    .catch(() => {
      tableData.value = [];
      loading.value = false;
    });
}

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-form
      ref="formRef"
      :inline="true"
      :model="form"
      class="clearfix"
      @submit.prevent
    >
      <el-form-item prop="time">
        <el-date-picker
          v-model="form.time"
          type="datetimerange"
          value-format="x"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59)
          ]"
          :clearable="false"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          @click="getList"
        >
          搜索
        </el-button>
      </el-form-item>
    </el-form>
    <RankTable :tableData="tableData" :tableHeader="tableHeader" />
  </div>
</template>
