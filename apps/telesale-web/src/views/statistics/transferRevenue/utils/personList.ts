/*
 * @Date         : 2024-04-01 15:31:30
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { TableColumns } from "/@/components/ReTable/types";

const listHeader: TableColumns[] = [
  {
    field: "followId",
    desc: "介绍人",
    minWidth: 100,
    idTransfer: "name",
    headerTip: true,
    idName: "followId",
    fixed: "left"
  },
  { field: "onionId", desc: "客户洋葱ID", fixed: true },
  { field: "phone", desc: "客户手机号", fixed: true },
  { field: "newUserNum", desc: "裂变新客户", sortable: true },
  {
    field: "payUserNum",
    desc: "转化新客户",
    sortable: true
  },
  {
    field: "orderNum",
    desc: "订单量",
    sortable: true,
    slot: { name: "orderNum" }
  },
  { field: "amount", desc: "订单金额/元", sortable: true },
  { field: "avgPrice", desc: "客单价/元" },
  { field: "convRate", desc: "转化率", sortable: true },
  {
    field: "workerName",
    desc: "线索归属坐席",
    minWidth: 100,
    idTransfer: "name",
    idName: "workerId",
    fixed: "right"
  }
];

const operation = [{ event: "details", text: "查看详情" }];

export { listHeader, operation };
