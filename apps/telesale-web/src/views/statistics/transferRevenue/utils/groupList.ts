import { ref, watch } from "vue";
import { useUserStoreHook } from "/@/store/modules/user";

function nameChange(row) {
  return row.convRate ? (row.convRate * 100).toFixed(2) + "%" : "0%";
}

const listHeader: any = ref([
  {
    field: "followId",
    desc: "介绍人",
    minWidth: 100,
    idTransfer: "name",
    idName: "followId",
    headerTip: true,
    fixed: "left"
  },
  {
    field: "followOrgId",
    desc: "介绍人所属小组",
    minWidth: 100,
    idTransfer: "orgNameList",
    idName: "followId",
    fixed: "left"
  },
  {
    field: "posterNum",
    desc: "下载海报客户量",
    sortable: true,
    minWidth: 155
  },
  {
    field: "oldUserNum",
    desc: "裂变老客户",
    sortable: true,
    minWidth: 125
  },
  {
    field: "newUserNum",
    desc: "裂变新客户",
    sortable: true,
    minWidth: 125
  },
  {
    field: "orderNum",
    desc: "订单量",
    sortable: true,
    minWidth: 95
  },
  {
    field: "amount",
    desc: "订单金额/元",
    sortable: true,
    minWidth: 130
  },
  {
    field: "avgPrice",
    desc: "客单价/元",
    sortable: true,
    minWidth: 115
  },
  {
    field: "convRate",
    desc: "转化率",
    filters: nameChange,
    sortable: true,
    minWidth: 95
  },
  {
    field: "workerName",
    desc: "线索归属坐席",
    minWidth: 100,
    idTransfer: "name",
    idName: "workerId",
    fixed: "right"
  },
  {
    field: "orgNameList",
    desc: "坐席所属小组",
    minWidth: 100,
    idTransfer: "orgNameList",
    idName: "workerId",
    fixed: "right"
  }
]);

export { listHeader };
