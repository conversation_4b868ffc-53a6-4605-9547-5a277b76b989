<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { FormInstance } from "element-plus";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useAppStoreHook } from "/@/store/modules/app";
import {
  getTransferPersonReferral,
  getTransferPersonReferralTotal
} from "/@/api/statistics";
import { getPlatformListApi } from "@telesale/server/src/api/active/transfer";
import paramsHandle from "/@/utils/handle/paramsHandle";
import originDate from "/@/utils/handle/originDate";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import RePagination from "/@/components/RePagination/index.vue";
import { listHeader, operation } from "../utils/personList";
import { totalList, sumInit } from "../utils/sumList";
import Details from "../dialog/Details.vue";
import AgentSelect from "/@/components/AgentSelect/index.vue";
import DetailsPayUser from "../dialog/DetailsPayUser.vue";

let device = useAppStoreHook().device;

const sum = ref({ ...sumInit });

//带分页列表数据必备
const loading = ref(true);
const dataList = ref([]);
const total = ref(0);
const platformList = ref([]);

//分页
const rePaginationRefs = ref();
function onSearch(val = false) {
  val === true && (rePaginationRefs.value.pageSize = 20);
  rePaginationRefs.value.onSearch();
}

//form查询
const form = reactive({
  onionId: "",
  phone: "",
  time: originDate(),
  orderBy: "",
  sortBy: "",
  workerId: undefined,
  platformId: undefined,
  followId: undefined
});

const formRef = ref<FormInstance>();

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  onSearch(true);
};

function getTotal() {
  return getTransferPersonReferralTotal(
    paramsHandle(form, { newTime: true, zero: ["platformId"] })
  )
    .then(({ data }: { data: any }) => {
      data.convRate = data.convRate ? (data.convRate * 100).toFixed(2) : "0";
      return data;
    })
    .catch(() => {
      return { ...sumInit };
    });
}

async function getList() {
  loading.value = true;
  sum.value = await getTotal();
  getTransferPersonReferral(
    paramsHandle(form, {
      newTime: true,
      zero: ["platformId"],
      pageIndex: rePaginationRefs.value.pageIndex,
      pageSize: rePaginationRefs.value.pageSize
    })
  )
    .then(({ data }: { data: any }) => {
      data.list.forEach(item => {
        item.convRate = item.convRate
          ? (item.convRate * 100).toFixed(2) + "%"
          : "0%";
      });
      dataList.value = data.list;
      total.value = data.total;
      loading.value = false;
    })
    .catch(() => {
      dataList.value = [];
      total.value = 0;
      loading.value = false;
    });
}

const isModel = ref<boolean>(false);
const PayUserModel = ref<boolean>(false);
const msg = ref();

function details(row) {
  msg.value = row;
  isModel.value = true;
}

//排序
function sortChange(column) {
  if (column.prop) {
    form.orderBy = column.prop;
    form.sortBy = column.order ? column.order.slice(0, -6) : "";
  }
  getList();
}

function parantMath({ key, params }) {
  switch (key) {
    case "details":
      details(params);
      break;
  }
}
const openDetails = row => {
  msg.value = row;
  PayUserModel.value = true;
};

// 获取平台列表
function getPlatformList() {
  getPlatformListApi()
    .then(({ data }) => {
      platformList.value = data.list || [];
    })
    .catch(() => {
      platformList.value = [];
    });
}

onMounted(() => {
  onSearch();
  getPlatformList();
});
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
      <el-form-item prop="followId">
        <AgentSelect v-model="form.followId" isAll placeholder="请选择介绍人" />
      </el-form-item>
      <el-form-item prop="workerId">
        <AgentSelect
          v-model="form.workerId"
          isAll
          placeholder="请选择归属坐席"
        />
      </el-form-item>
      <el-form-item prop="phone">
        <el-input
          v-model.trim="form.phone"
          placeholder="请输入客户手机号"
          clearable
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item prop="onionId">
        <el-input
          v-model.trim="form.onionId"
          placeholder="请输入客户洋葱ID"
          clearable
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item prop="platformId">
        <el-select v-model="form.platformId" placeholder="请选择平台" clearable>
          <el-option
            v-for="item in platformList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="time">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          value-format="x"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59)
          ]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <div class="g-list-box">
      <div v-for="(item, i) in totalList" :key="i">
        <span class="title-name">{{ item.label }}：</span>
        <span class="text-name">{{ sum[item.key] }}{{ item.addStr }}</span>
      </div>
    </div>
    <div class="g-table-box">
      <ReTable
        v-if="device !== 'mobile'"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
        :sortChange="sortChange"
        @parantMath="parantMath"
      >
        <template #orderNum="{ row }">
          <div>
            <el-button type="primary" link @click="openDetails(row)">
              {{ row.orderNum }}
            </el-button>
          </div>
        </template>
      </ReTable>
      <template v-else>
        <ReCardList
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
          @parantMath="parantMath"
        >
          <template #orderNum="{ row }">
            <div>
              <el-button type="primary" link @click="openDetails(row)">
                {{ row.orderNum }}
              </el-button>
            </div>
          </template>
        </ReCardList>
      </template>
    </div>
    <RePagination ref="rePaginationRefs" :total="total" @getList="getList" />
    <Details
      v-if="isModel"
      v-model:value="isModel"
      :msg="msg"
      :platformId="form.platformId"
      :time="form.time"
    />
    <DetailsPayUser
      v-if="PayUserModel"
      v-model:value="PayUserModel"
      :msg="msg"
      :platformId="form.platformId"
      :time="form.time"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item .el-input) {
  width: 170px !important;
}
</style>
