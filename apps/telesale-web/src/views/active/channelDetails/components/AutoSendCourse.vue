<script setup lang="ts">
import { computed } from "vue";

interface Props {
  form: any;
  type: string;
}
const props = defineProps<Props>();

interface Emits {
  (e: "update:form", val: any): void;
}

const emit = defineEmits<Emits>();

const form = computed({
  get() {
    return props.form;
  },
  set(val: any) {
    emit("update:form", val);
  }
});

function changeHome() {
  form.value.courses = [];
  form.value.authDay = "";
}

const propsParams = { multiple: true, emitPath: false };
const courseList = [
  {
    value: "sync",
    label: "同步课",
    children: [
      {
        value: 1,
        label: "小学",
        children: [
          {
            value: "vip#1-1",
            label: "数学"
          },
          {
            value: "vip#1-8",
            label: "自然"
          },
          {
            value: "vip#1-9",
            label: "地球"
          },
          {
            value: "vip#1-10",
            label: "实验"
          },
          {
            value: "vip#1-3",
            label: "语文"
          }
        ]
      },
      {
        value: 2,
        label: "初中",
        children: [
          {
            value: "vip#2-1",
            label: "数学"
          },
          {
            value: "vip#2-5",
            label: "英语"
          },
          {
            value: "vip#2-3",
            label: "语文"
          },
          {
            value: "vip#2-6",
            label: "生物"
          },
          {
            value: "vip#2-2",
            label: "物理"
          },
          {
            value: "vip#2-4",
            label: "化学"
          },
          {
            value: "vip#2-7",
            label: "地理"
          }
        ]
      },
      {
        value: 3,
        label: "高中",
        children: [
          {
            value: "vip#3-1",
            label: "数学"
          },
          {
            value: "vip#3-2",
            label: "物理"
          },
          {
            value: "vip#3-6",
            label: "生物"
          },
          {
            value: "vip#3-4",
            label: "化学"
          },
          {
            value: "vip#3-5",
            label: "英语"
          }
        ]
      }
    ]
  },
  {
    value: "custom",
    label: "培优课",
    children: [
      {
        value: 1,
        label: "小学",
        children: [
          {
            value: "a820e7f0-4774-11ec-99c2-7367123a3632",
            label: "小学数学总复习培优课"
          }
        ]
      },
      {
        value: 2,
        label: "初中",
        children: [
          {
            value: "73f2d910-ecd4-11ee-8f65-4a156de36ad3",
            label: "初中数学重难点培优课-通用版"
          },
          {
            value: "1caa6bab-0146-11ef-9272-1ad70eaae358",
            label: "初中物理重难点培优课-通用版"
          },
          {
            value: "260674bf-3456-11ef-9e4a-8e9d59d085aa",
            label: "初中化学重难点培优课-通用版"
          },
          {
            value: "0c650748-02bd-11ef-84d1-aea51e33dbea",
            label: "初中语文重难点培优课"
          },
          {
            value: "65a12f78-0159-11ef-8577-9e782df4fecc",
            label: "初中英语重难点培优课-通用版"
          },
          {
            value: "b0400f23-a222-11ef-88f9-16aad193f435",
            label: "初中生物重难点培优课-通用版"
          },
          {
            value: "91d9b5a5-a222-11ef-a527-c6630ade03df",
            label: "初中地理重难点培优课-通用版"
          }
        ]
      },
      {
        value: 3,
        label: "高中",
        children: [
          {
            value: "2bd05600-f598-11eb-a031-33dd886fa8a3",
            label: "高中数学一轮复习培优课"
          },
          {
            value: "1988e4fd-f86c-11ed-9740-56a786333721",
            label: "高中化学一轮复习培优课"
          },
          {
            value: "afdcca5f-f86e-11ed-9740-56a786333721",
            label: "高中物理一轮复习培优课"
          },
          {
            value: "1150e31b-7c49-11ed-b2b0-567debafa38e",
            label: "高中生物一轮复习培优课"
          },
          {
            value: "0d6e797b-23da-11ef-a12a-e698fa38aecc",
            label: "高中英语一轮复习培优课"
          }
        ]
      }
    ]
  }
];
</script>

<template>
  <el-form-item label="自动发放课程" prop="isAuthCourses">
    <el-switch
      @change="changeHome"
      v-model="form.isAuthCourses"
      :disabled="type === 'detail'"
    />
  </el-form-item>
  <el-form-item
    label="科目"
    prop="courses"
    v-if="form.isAuthCourses"
    :rules="{
      required: true,
      message: '请选择科目',
      trigger: 'change'
    }"
  >
    <el-cascader
      v-model="form.courses"
      placeholder="请选择科目"
      separator=""
      :options="courseList"
      :props="propsParams"
      clearable
      :disabled="type === 'detail'"
    />
  </el-form-item>

  <el-form-item
    prop="authDay"
    label="赠送天数"
    v-if="form.isAuthCourses"
    :rules="{
      required: true,
      message: '请选择赠送天数',
      trigger: 'change'
    }"
  >
    <el-select
      v-model="form.authDay"
      placeholder="请选择赠送天数"
      :disabled="type === 'detail'"
    >
      <el-option label="1天" :value="1" />
      <el-option label="2天" :value="2" />
      <el-option label="3天" :value="3" />
      <el-option label="7天" :value="7" />
      <el-option label="10天" :value="10" />
      <el-option label="21天" :value="21" />
      <el-option label="30天" :value="30" />
    </el-select>
  </el-form-item>
</template>
<style scoped lang="scss">
:deep(.el-cascader) {
  width: 100%;
  .el-input {
    width: 100%;
  }
}
</style>
