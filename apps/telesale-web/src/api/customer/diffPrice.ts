/*
 * @Date         : 2025-01-22 14:07:08
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "../../utils/http";
import baseURL from "../url";

export interface DiffPriceReq {
  userid?: string;
}

export interface DiffPriceInfo {
  strategyId?: string;
  strategyName?: string;
  maxDeductAmount: number;
  deductibleOrders?: {
    id: string;
    name: string;
    deductAmount: number;
    deductCategory: string;
  }[];
  reasonDetail?: string[];
  deductAmount?: number;
  amount?: number;
  showSum: boolean;
}
[];

export interface DiffPriceInfoRes {
  list?: DiffPriceInfo[];
}

/**
 * @description 根据用户id查询补差价详情-2025年1月
 * https://yapi.yc345.tv/project/2352/interface/api/120498
 * <AUTHOR>
 * @date 2025-01-22
 * @export
 * @param {DiffPriceReq} data
 * @returns {Promise<DiffPriceInfoRes>}
 */
export const getNewDiffPriceListApi = (data: DiffPriceReq) => {
  return http.request<DiffPriceInfoRes>(
    `post`,
    `${baseURL.api}/wuhan-datapool/diffprice/query/v6`,
    {
      data
    }
  );
};
