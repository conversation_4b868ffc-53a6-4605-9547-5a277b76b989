/*
 * @Date         : 2024-07-31 10:31:54
 * @Description  : 坐席管理-坐席列表-表格表头名词解释
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

//表格表头名词解释提示
export default {
  aClass: "A类：续费线索",
  bClass:
    "B类：非续费线索且注册时长<=30天且【家长身份用户或（一二三线城市且有性别且未被电销触达过）】",
  cClass: "C类：非续费线索且注册时长<=30天中的非B类线索",
  dClass:
    "D类：非续费线索且注册时长>30天且【家长身份用户或（一二三线城市且未被电销触达过）】",
  eClass: "E类：非续费线索且注册时长>30天中的非D类线索",
  firstDialDuration: "第一次拨打的时间距离领取线索的时间间隔",
  usertype: "新增：历史单次付费小于39；<br/>付费：历史单次付费大于等于39",
  userType: "新增：历史单次付费小于39；<br/>付费：历史单次付费大于等于39",
  isVIP: "截止领取时间购买课程是否到期",
  createdAt: "在当前坐席名下线索的创建时间",
  userExpire: "在当前坐席名下线索的到期时间",
  lastDial: "线索在所归属坐席名下的最近一次外呼的时间",
  firstValidDial: "第一次通话时长大于10秒的外呼时间",
  total: "拨打的线索总数（1条线索多次拨打只计算1次）",
  callClueCount: "拨打的线索总数（1条线索多次拨打只计算1次）",
  convRate: "领取线索成单数/拨打线索数",
  time: "有效通话定义：单次外呼沟通时长>=10秒",
  validCallLength: "有效通话定义：单次外呼沟通时长>=10秒",
  rate: "有效通话的线索数/拨打线索数",
  validCallRate: "有效通话量/拨打线索量",
  recentlyActiveDay:
    "最近3天指T-3至T-1，只要某一天用户有一次活跃行为则算做这一天活跃了",
  historyAmount: "指用户入库之前的所有支付成功订单金额总和",
  followId: "介绍人指用户扫描谁的海报参加的活动"
};
