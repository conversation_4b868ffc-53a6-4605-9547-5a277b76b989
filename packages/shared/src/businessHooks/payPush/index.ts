import { ref, reactive, computed, watch } from "vue";
import { isEqualStage } from "../../utils";
import { getDiffHideIds, ipadNoneMap, repurchaseTypeObj } from "./utils";
import { GoodList, GoodsConfig, IpadGoodInfo } from "./types";
import { getInstallmentPayType } from "./installmentPay";
import { bigVipList } from "../../data/customer";
import { cloneDeep } from "lodash-es";
import { useNewSyncLink } from "../newSyncLink";
import { getCourseAppListApi } from "@telesale/server/src/api/customer/details";

interface Options {
  userId: string;
  onionid: string;
  mode: string;
  stageInfo: {
    stage?: string;
    grade?: string;
  };
  priceDiffFindNew: Function;
  priceDiffFind: Function;
  repurchaseFind: Function;
  getCourseListApi: Function;
  getGoodConfigApi: Function;
  getTagListApi: Function;
  getOrgData?: Function;
  isExperimentGroupWorkerApi: Function;
  getNewDiffPriceListApi: Function;
  getDiffSettingApi: Function;
  getGoodInfoApi: any;
  getSyncDataApi: any;
  getVenueLinkGoodsListApi: any;
}

export interface ClassTarget {
  id: number | string;
  name: string;
  strategyType: string;
  amount: number;
  discountPrice: number;
  diffPrice: number;
  paidAmount: number;
  shouldPay: number;
  paidOrderId: number;
  skuGoodId: string;
  courseId: string;
  strategyId: string;
  TargetGoodId: string;
  skuGoodName: string;
  originalAmount: number;
  reducePrice: number;
  directAmount: number;
  otherAmount: number;
  deductAmount: number;
  packGood?: any[];
  strategyName?: string;
  category: string;
  showSum: boolean;
  addContent: any[];
  OriginAmount: number;
  recommend: string;
}

export const usePayPush = (options: Options) => {
  const {
    mode,
    userId,
    onionid,
    stageInfo,
    priceDiffFindNew,
    priceDiffFind,
    repurchaseFind,
    getCourseListApi,
    getNewDiffPriceListApi,
    getGoodConfigApi,
    getTagListApi,
    getOrgData,
    isExperimentGroupWorkerApi,
    getDiffSettingApi,
    getGoodInfoApi,
    getSyncDataApi,
    getVenueLinkGoodsListApi
  } = options;

  const {
    syncData,
    loading: loadingSyncData,
    goodList: blocksGood,
    goodInfo,
    changeSchoolYear,
    getData,
    getGoodInfo
  } = useNewSyncLink({
    userId,
    immediate: false,
    getSyncDataApi,
    getGoodInfoApi,
    getVenueLinkGoodsListApi
  });

  const diffList = ref();
  const diffNewList = ref();
  const linkList = ref();
  const showList = ref<any[]>([]);
  const showNewList = ref<any[]>([]);
  const linkShowList = ref([]);

  const typeList = ref<any[]>([]);
  const repurchaseMap = ref({});

  const courseTagsList = ref([]);
  const orgIds = ref([]);
  const loading = ref<boolean>(false);
  const initList = ref([]);
  const bigVipType = ref("");
  const courseType = ref("");
  const goodList = ref<GoodsConfig[]>([]);
  const goodsMobile = ref<string | undefined>(undefined);
  const ipadTypeList = ref<IpadGoodInfo[]>([]);
  const form = reactive({
    onionid: onionid,
    pushType: "",
    target: null as ClassTarget | null,
    schoolYear: "",
    duration: undefined,
    isNewExclusiveLink: false,
    isPackGood: false,
    exchange: "",
    type: "",
    name: "",
    orderId: "",
    vipType: "common",
    stage: "1",
    goods: undefined as GoodList | undefined,
    isInstallment: 2,
    installmentPayType: undefined,
    addPad: undefined
  });
  const rules = {
    onionid: [
      {
        required: true,
        trigger: "blur"
      }
    ],
    pushType: [
      {
        required: true,
        message: "请选择购买类型",
        trigger: "change"
      }
    ],
    exchange: [
      {
        required: true,
        message: "请选择平板",
        trigger: "change"
      }
    ],
    stage: [
      {
        required: true,
        message: "请选择学段",
        trigger: "change"
      }
    ],
    type: [
      {
        required: true,
        message: "请选择续购类型",
        trigger: "change"
      }
    ],
    isInstallment: [
      {
        required: true,
        message: "请选择分期支付",
        trigger: "change"
      }
    ],
    installmentPayType: [
      {
        required: true,
        message: "请选择分期支付方式",
        trigger: "change"
      }
    ]
  };

  const linkFilterData = computed(() => {
    if (form.pushType === "link" && courseType.value) {
      let ids: any = null;
      if (courseType.value) {
        ids = [];
        courseTagsList.value.forEach(item => {
          const name = item.name?.split("-")?.[0];
          if (name === courseType.value) {
            ids.push(item.id);
          }
        });
      }
      return ids
        ? linkShowList.value.filter(item =>
            item.courseTags.some(tag => ids.includes(tag))
          )
        : [];
    } else {
      return linkShowList.value;
    }
  });

  const changeBigVip = () => {
    const list = bigVipType.value
      ? initList.value.filter((item: any) =>
          isEqualStage(item.targetUserStage, bigVipType.value)
        )
      : initList.value;
    form.target = null;
    showList.value = diffList.value = groupHandle(list);
  };

  const changeNewStage = () => {
    const list = bigVipType.value
      ? diffNewList.value.filter((item: any) => item.stage === bigVipType.value)
      : diffNewList.value;
    form.target = null;
    showNewList.value = list;
  };

  const changeCourseType = () => {
    form.target = null;
  };

  const handleData = () => {
    const data: any = {
      ...form,
      userId,
      exchange: form.exchange || ""
    };

    const whiteType = ["blocksGood", "exclusiveLink"];

    if (!whiteType.includes(form.pushType)) {
      if (data.pushType === "link") {
        if (!form.isNewExclusiveLink) {
          // data.exclusiveLinkId = form.target.id;
          data.name = data.target.name;
          data.skuGoodId = form.target?.courseId;
          data.pushType = "link_v2";
        }
      } else if (data.pushType === "difference_v6") {
        data.skuGoodId = form.target?.skuGoodId;
        data.name = form.target?.skuGoodName;
        data.differenceId = form.target.strategyId;
      } else if (data.pushType === "linkPad") {
        data.kind = "onlyPad";
        data.name = data.exchange;
      } else if (data.pushType === "zhufengPre2024") {
        data.name = "省钱卡";
      } else {
        data.differenceId = form.target.id;
        data.strategyType = form.target.strategyType;
        if (!form.type || form.type === "activity") {
          data.name = data.target?.goodName;
          data.amount = form.target?.diffPrice;
        }
      }
    }

    if (data.pushType === "blocksGood") {
      if (data.isPackGood) {
        data.target = {
          ...data.target,
          ...data.target.packGood[0]
        };
      }
      data.differenceId = data.target.id;
      data.name = data.target.strategyName;
      data.category = data.target.category;
    }

    if (form.pushType === "repurchase") {
      data.type = "commonXugou";
      data.amount = form.target.amount;
      data.orderId = form.target.paidOrderId;
      data.strategyId = form.target.strategyId;
      data.targetGoodId = form.target.TargetGoodId;
      data.name = data.target.name;
      data.differenceId = "";
    }
    !data.duration && delete data.duration;
    delete data.target;

    if (form.isNewExclusiveLink) {
      data.pushType = "link";
      const { param = undefined } = form.goods;
      data.stage = param?.stage ? param?.stage + "" : "";
      data.goodType = param?.goodType;
      data.schoolYear = param?.schoolYear || "";
      data.duration = param?.duration;
      data.name = form.goods?.name;
      if (form.stage === "high") {
        data.displayName = form.goods?.cloneName;
      } else {
        data.displayName = form.goods?.name;
      }
      Reflect.deleteProperty(data, "goods");
    }
    data.installmentPayType = getInstallmentPayType(
      data.isInstallment,
      data.installmentPayType
    );
    Reflect.deleteProperty(data, "isInstallment");

    if (form.addPad) {
      const pad = form.target.addContent?.find(
        item => item.label === form.addPad
      );
      data.addContent = !pad.label ? [] : [pad.label];
      Reflect.deleteProperty(data, "addPad");
    }

    return data;
  };

  function filterDiff(val) {
    const list =
      form.pushType === "difference_v6" ? diffNewList.value : diffList.value;
    if (val === undefined || val === "") {
      showList.value = list;
    } else {
      showList.value = JSON.parse(JSON.stringify(list)).map(group => {
        group.options = group.options.filter(item => {
          if (item.nameInit.indexOf(val) === -1) {
            return false;
          }
          let regVal = val;
          if (regVal.indexOf(".") > -1) {
            regVal = regVal.split(".").join("\\.");
          }
          item.name = item.name.replace(
            new RegExp(regVal, "g"),
            `<span style="color:#F56C6C">${val}</span>`
          );
          return true;
        });
        return group;
      });
    }
  }

  const filterNewDiff = (val: string) => {
    const list = diffNewList.value;
    if (val === undefined || val === "") {
      return;
    }
    showNewList.value = cloneDeep(list).filter(item => {
      if (item.nameInit.indexOf(val) === -1) {
        return false;
      }
      let regVal = val;
      if (regVal.indexOf(".") > -1) {
        regVal = regVal.split(".").join("\\.");
      }
      item.name = item.name.replace(
        new RegExp(regVal, "g"),
        `<span style="color:#F56C6C">${val}</span>`
      );
      return true;
    });
  };

  const groupHandle = arr => {
    const priceDiffList: any = [];
    arr.forEach((item: any) => {
      const index = priceDiffList.findIndex(
        find => find.padType === item.padType
      );

      if (index > -1) {
        priceDiffList[index].options.push(item);
      } else {
        priceDiffList.push({
          padType: item.padType,
          label: `${item.padType ? `带${item.padType}` : "不带"}平板商品`,
          options: [item]
        });
      }
    });
    return priceDiffList.filter((item: any) => item.options.length > 0);
  };

  async function diffListMath() {
    loading.value = true;
    if (getOrgData) {
      const res = await getOrgData?.();
      const ids = res.data.pathList?.reduce((pre, cur) => {
        return pre.concat(cur.id?.split?.(","));
      }, []);
      orgIds.value = [...new Set(ids)];
    }
    priceDiffFind({ userid: userId })
      .then(async ({ data }: { data: any }) => {
        const resData = data.list;

        const list = resData.filter(item => {
          item.amount = item.amount.toFixed(2);
          item.name = "【￥" + item.amount + "】" + item.goodName;
          item.nameInit = item.name;
          item.id = item.strategyId;
          if (form.pushType === "difference_v4" || form.type === "activity") {
            const authList = [];
            item.discountPrice = 0;
            item.deductibleOrders.forEach(userAuth => {
              userAuth.amountName = "￥" + userAuth.amount.toFixed(2);
              item.discountPrice += userAuth.amount;
            });
            if (form.pushType === "difference_v4") {
              item.deductibleVipAuth?.forEach(deductibleVipAuth => {
                item.discountPrice += deductibleVipAuth.amount;
                authList.push(deductibleVipAuth);
              });
              item.deductibleSpecialAuth?.forEach(deductibleSpecialAuth => {
                item.discountPrice += deductibleSpecialAuth.amount;
                authList.push(deductibleSpecialAuth);
              });
              item.deductibleAuthDetailList = authList;
            }
            item.diffPrice = (item.amount - item.discountPrice).toFixed(2);
            item.discountPrice = item.discountPrice.toFixed(2);
          } else {
            item.discountPrice = (item.amount - item.diffPrice).toFixed(2);
            item.diffPrice = item.diffPrice.toFixed(2);
          }
          return (
            item.joinedActivity ||
            form.pushType === "difference_v4" ||
            form.type === "activity"
          );
        });

        initList.value = list;
        const activeData = [];
        const diffData = [];
        list.forEach(item => {
          if (item.deductCategory === "bigVip") {
            activeData.push(item);
          } else {
            diffData.push(item);
          }
        });
        diffList.value = groupHandle(diffData);
        showList.value = diffList.value;
        loading.value = false;
      })
      .catch(() => {
        loading.value = false;
      });
  }

  const getNewDiffPriceList = () => {
    loading.value = true;
    getNewDiffPriceListApi({ userid: userId })
      .then(async res => {
        const diffSettingData = await getDiffSettingApi();
        const hideIds = getDiffHideIds(diffSettingData, true);
        const list = res.data.list.filter((item, index) => {
          item.id = index + 1;
          item.name = "【￥" + item.amount + "】" + item.skuGoodName;
          item.nameInit = item.name;
          item.discountPrice = item.deductAmount;
          item.diffPrice = (item.amount - item.deductAmount).toFixed(2);
          item.reducePrice = item.originalAmount - item.amount;
          item.directAmount = 0;
          item.otherAmount = 0;
          item.deductAmountSum = 0;
          item.showSum = true;
          item.deductibleOrders.forEach(ded => {
            item.deductAmountSum += ded.deductAmount;
            if (ded.deductCategory === "direct") {
              item.directAmount += ded.deductAmount;
            } else {
              item.otherAmount += ded.deductAmount;
            }
          });

          if (item.deductAmountSum > item.deductAmount) {
            item.showSum = false;
            item.otherAmount = item.deductAmount;
          }

          return item.canJoin && !hideIds.includes(item.skuGoodId);
        });

        showNewList.value = list;
        diffNewList.value = list;
        changeNewStage();
      })
      .finally(() => {
        loading.value = false;
      });
  };

  function repurchaseListMath() {
    loading.value = true;
    repurchaseFind({ uid: userId, sellfrom: "telesale" })
      .then(({ data }: { data: any }) => {
        for (const key in data) {
          if (Object.prototype.hasOwnProperty.call(data, key)) {
            if (data[key]?.length) {
              data[key].forEach((item, index) => {
                item.id = index;
                item.amount = item.amount.toFixed(2);
                item.OriginAmount = item.OriginAmount.toFixed(2);
              });
            }
          }
        }
        data.common.forEach(item => {
          if (repurchaseMap.value[item.commonXugouType]) {
            repurchaseMap.value[item.commonXugouType].push(item);
          } else {
            repurchaseMap.value[item.commonXugouType] = [item];
            typeList.value.push({
              name: item.commonXugouTypeText,
              value: item.commonXugouType
            });
          }
        });

        console.log("repurchaseMap.value", repurchaseMap.value, typeList.value);

        linkShowList.value = [];
        loading.value = false;
      })
      .catch(() => {
        linkShowList.value = [];
        loading.value = false;
      });
  }

  async function linkListMath() {
    loading.value = true;
    try {
      await getCourseTagsList();
    } catch {
      courseTagsList.value = [];
    }
    getCourseListApi({
      pageSize: 500,
      status: "已上架"
    })
      .then(async ({ data }: { data: any }) => {
        const res = await getCourseAppListApi({ userId: userId });
        res.data.list.forEach(item => {
          item.id = item.courseId;
          item.type = "appCourse";
        });
        data.list.unshift(...res.data.list);

        linkShowList.value = linkList.value = data.list;
        loading.value = false;
      })
      .catch(() => {
        linkShowList.value = linkList.value = [];
        loading.value = false;
      });
  }

  const getCourseTagsList = async () => {
    const { data } = await getTagListApi();
    courseTagsList.value = data.list;
  };

  async function changeType(val) {
    clearValue();
    form.isNewExclusiveLink = false;
    linkShowList.value = [];
    if (val === "link") {
      linkList.value ? (linkShowList.value = linkList.value) : linkListMath();
    } else if (val === "difference_v6") {
      diffNewList.value
        ? (showNewList.value = diffNewList.value)
        : await getNewDiffPriceList();
    } else if (val === "repurchase") {
      await repurchaseListMath();
    } else if (val === "difference_v4") {
      diffList.value ? (showList.value = diffList.value) : await diffListMath();
      changeBigVip();
    }

    if (val === "exclusiveLink") {
      form.isNewExclusiveLink = true;
      getGoodsConfig();
    }

    if (val === "blocksGood") {
      if (!syncData.value.length) {
        await getData();
      }
      getSchoolStage();
      changeSchoolYear(form.schoolYear);
    }

    if (val === "difference_v6") {
      bigVipType.value = stageInfo.stage;
    }
  }

  const changeInstallment = () => {
    form.installmentPayType = ["alipayFq"];
  };

  const changeTypeHandle = async (e: string) => {
    form.target = null;
    bigVipType.value = "";
    linkShowList.value = repurchaseMap.value[e];
  };

  function changeVipType() {
    form.exchange = "";
    form.duration = undefined;
    form.schoolYear = "";
  }

  function clearValue(val: boolean = false) {
    if (val) {
      getGoodsConfig();
    }
    changeVipType();
    courseType.value = "";
    form.target = null;
    bigVipType.value = "";
    form.isPackGood = false;
    form.type = "";
    form.vipType = "common";
    form.stage = goodList.value?.[0]?.cname || undefined;
    form.goods = undefined;
    form.schoolYear = "";
    goodsMobile.value = undefined;
    goodInfo.value = undefined;
    if (form.type === "blocksGood") {
      getSchoolStage();
    }
  }

  const getSchoolStage = () => {
    if (stageInfo.grade) {
      const oneToFour = ["一年级", "二年级", "三年级"];
      form.schoolYear = oneToFour.includes(stageInfo.grade)
        ? "三年级"
        : stageInfo.grade;
      console.log("form.schoolYear", form.schoolYear);
    } else {
      const gradeMap = {
        小学: "三年级",
        初中: "七年级",
        高中: "高一"
      };
      form.schoolYear = gradeMap[stageInfo.stage];
    }
  };

  const changeStage = () => {
    form.duration = "";
    form.goods = undefined;
    goodsMobile.value = undefined;
    form.exchange = "";
    form.schoolYear = "";
  };

  function changeTarget() {
    form.schoolYear = "";
  }

  const changeGood = (e?: any) => {
    if (e) {
      e?.addPads?.forEach(item => {
        item.value = item.label;
      });
      ipadNoneMap[0].amount = e.price || "";
      ipadTypeList.value = [...ipadNoneMap, ...e.addPads];
    }
    form.exchange = "";
  };

  const getGoodsConfig = () => {
    if (goodList.value.length === 0) {
      loading.value = true;
      getGoodConfigApi()
        .then(res => {
          res.data.data.stage.forEach(item => {
            item.goodList.forEach(good => {
              good.cloneName = good.name;
              if (item.cname === "high") {
                good.name += `-${good.price}元`;
              } else {
                good.name += `-${good.year}年`;
              }
            });
          });
          goodList.value = res.data.data.stage;
          form.stage = goodList.value?.[0]?.cname || undefined;
        })
        .finally(() => {
          loading.value = false;
        });
    }
  };

  const changeBlockSchoolYear = () => {
    form.target = undefined;
    goodsMobile.value = undefined;
    goodInfo.value = undefined;
    form.isPackGood = false;
    changeSchoolYear(form.schoolYear);
  };

  const changeblocksGood = () => {
    form.isPackGood = false;
    form.addPad = form.target?.addContent?.[0]?.label;
    getGoodInfo(form.target?.id as string, form.addPad);
  };

  const changeUpgrade = () => {
    if (form.isPackGood) {
      getGoodInfo(form.target?.packGood[0]?.id as string, form.addPad);
      return;
    }
    getGoodInfo(form.target?.id as string, form.addPad);
  };

  watch(
    () => loadingSyncData.value,
    () => {
      loading.value = loadingSyncData.value;
    },
    {
      deep: true,
      immediate: true
    }
  );

  return {
    rules,
    diffList,
    diffNewList,
    linkList,
    showList,
    showNewList,
    linkShowList,
    linkFilterData,
    typeList,
    loading,
    initList,
    bigVipType,
    courseType,
    form,
    bigVipList,
    goodList,
    goodsMobile,
    ipadTypeList,
    syncData,
    blocksGood,
    goodInfo,
    changeblocksGood,
    changeUpgrade,
    changeBlockSchoolYear,
    changeTypeHandle,
    changeVipType,
    clearValue,
    changeTarget,
    changeStage,
    changeType,
    changeNewStage,
    filterDiff,
    filterNewDiff,
    changeBigVip,
    changeCourseType,
    changeGood,
    changeInstallment,
    handleData
  };
};
